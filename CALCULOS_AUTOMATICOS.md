# 🧮 Sistema de Cálculos Automáticos - Operações

## 🎯 Visão Geral

O sistema de cálculos automáticos foi implementado para eliminar erros manuais e garantir consistência nos dados de performance das operações logísticas. Os campos são calculados automaticamente baseados na comparação entre horários agendados e reais.

## ⚡ Funcionalidades Automáticas

### 📊 **Performance de Carregamento**
**Regra de Cálculo:**
- **NO PRAZO**: Se Data/Hora Chegada Carga ≤ Data/Hora Agendada P/ Carregamento
- **ATRASADO**: Se Data/Hora Chegada Carga > Data/Hora Agendada P/ Carregamento
- **Vazio**: Se não houver Data/Hora Chegada Carga

**Exemplo:**
```
Agendado: 05/06/2025 07:00:00
Chegada:  05/06/2025 06:30:00
Resultado: "NO PRAZO"

Agendado: 05/06/2025 07:00:00
Chegada:  05/06/2025 08:15:00
Resultado: "ATRASADO"
```

### 📊 **Performance de Descarga**
**Regra de Cálculo:**
- **NO PRAZO**: Se Data/Hora Chegada Descarga ≤ Data/Hora Agendada P/ Descarga
- **ATRASADO**: Se Data/Hora Chegada Descarga > Data/Hora Agendada P/ Descarga
- **Vazio**: Se não houver Data/Hora Chegada Descarga

### ⏱️ **Tempo em Carga**
**Regra de Cálculo:**
- Diferença entre Data/Hora Fim Carregamento e Data/Hora Início de Carga
- Formato: "Xh Ymin" (ex: "2h 30min")
- **Vazio**: Se não houver ambas as datas

**Exemplo:**
```
Início:  05/06/2025 07:00:00
Fim:     05/06/2025 09:30:00
Resultado: "2h 30min"
```

### ⏱️ **Tempo de Descarga**
**Regra de Cálculo:**
- Diferença entre Data/Hora Fim Descarga e Data/Hora Início de Descarga
- Formato: "Xh Ymin" (ex: "1h 45min")
- **Vazio**: Se não houver ambas as datas

### 🚨 **Causa do Atraso (Automática)**
**Regra de Cálculo:**
- **"Atraso na chegada"**: Se performance = ATRASADO
- **"Não houve atraso"**: Se performance = NO PRAZO
- **Mantém valor manual**: Se já existe uma causa personalizada
- **Vazio**: Se não houver dados suficientes

### 💰 **Diárias C (Carregamento)**
**Regra de Cálculo:**
- **Atraso para Iniciar**: Se cliente demorou para começar o carregamento
- **Tempo Total**: Do início ao fim do carregamento (sempre conta)
- **Formato**: "Xh Ymin" (ex: "3h 30min")
- **Vazio**: Se não houver horários de início e fim

### 💰 **Diárias D (Descarregamento)**
**Regra de Cálculo:**
- **Herança**: Considera toda diária do carregamento
- **Atraso para Iniciar**: Ajustado pela herança do carregamento
- **Tempo Total**: Do início ao fim da descarga (sempre conta)
- **Formato**: "Xh Ymin" (ex: "4h 15min")
- **Vazio**: Se não houver horários de início e fim

## 🔧 Implementação Técnica

### 📁 **Arquivo Principal**
```typescript
// src/lib/operationCalculations.ts
export const calculateOperationFields = (operation: Partial<Operation>): Partial<Operation>
```

### 🎛️ **Campos Calculados Automaticamente**
1. **performanceCarregamento** - Performance de Carregamento
2. **performanceDescarga** - Performance de Descarga
3. **tempoEmCarga** - Tempo em Carga
4. **tempoDescarga** - Tempo de Descarga
5. **causaAtrazoCarregamento** - Causa do Atraso (se não preenchida manualmente)
6. **causaAtrazoDescarga** - Causa do Atraso (se não preenchida manualmente)
7. **diariasC** - Diárias de Carregamento (atraso do cliente)
8. **diariasD** - Diárias de Descarregamento (atraso do cliente + herança)

### 🔄 **Gatilhos de Recálculo**
Os cálculos são executados automaticamente quando os seguintes campos são alterados:
- `dataHoraAgendadaCarregamento`
- `dataHoraChegadaCarga`
- `dataHoraInicioCarga`
- `dataHoraFimCarregamento`
- `dataHoraAgendadaDescarga`
- `dataHoraChegadaDescarga`
- `dataHoraInicioDescarga`
- `dataHoraFimDescarga`

## 📝 **Formato de Data/Hora**

### ✅ **Formato Aceito**
```
DD/MM/YYYY HH:mm:ss
DD/MM/YYYY HH:mm

Exemplos válidos:
- 05/06/2025 07:00:00
- 15/12/2024 14:30:15
- 01/01/2025 00:00
```

### ❌ **Formatos Inválidos**
```
- 2025-06-05 07:00:00 (formato ISO)
- 06/05/2025 07:00:00 (formato americano)
- 05-06-2025 07:00:00 (separador incorreto)
```

### 🛡️ **Validação**
- Validação automática no formulário
- Mensagem de erro específica para formato inválido
- Campos vazios são aceitos (opcional)

## 🎨 **Interface do Usuário**

### 🔒 **Campos Somente Leitura**
Os campos calculados automaticamente são exibidos como **somente leitura** no formulário:
- Fundo acinzentado (`bg-muted/50`)
- Cursor "não permitido" (`cursor-not-allowed`)
- Label indica "(Calculado)" 
- Placeholder: "Calculado automaticamente"

### 🔄 **Atualização em Tempo Real**
- Cálculos executados **instantaneamente** ao digitar
- Sem necessidade de salvar para ver o resultado
- Feedback visual imediato

## 📋 **Exemplos Práticos**

### 🟢 **Cenário 1: Operação No Prazo**
```
Dados de Entrada:
- Agendamento Carregamento: 05/06/2025 07:00:00
- Chegada Carga: 05/06/2025 06:45:00
- Início Carga: 05/06/2025 07:00:00
- Fim Carregamento: 05/06/2025 09:30:00

Resultados Calculados:
- Performance Carregamento: "NO PRAZO"
- Tempo em Carga: "2h 30min"
- Causa Atraso: "Não houve atraso"
```

### 🔴 **Cenário 2: Operação Atrasada**
```
Dados de Entrada:
- Agendamento Carregamento: 05/06/2025 07:00:00
- Chegada Carga: 05/06/2025 08:30:00
- Início Carga: 05/06/2025 09:00:00
- Fim Carregamento: 05/06/2025 11:15:00

Resultados Calculados:
- Performance Carregamento: "ATRASADO"
- Tempo em Carga: "2h 15min"
- Causa Atraso: "Atraso na chegada"
```

### ⚪ **Cenário 3: Dados Incompletos**
```
Dados de Entrada:
- Agendamento Carregamento: 05/06/2025 07:00:00
- Chegada Carga: (vazio)

Resultados Calculados:
- Performance Carregamento: (vazio)
- Tempo em Carga: (vazio)
- Causa Atraso: (vazio)
```

## 🔮 **Benefícios**

### ✅ **Para Operadores**
- **Eliminação de erros manuais** de cálculo
- **Consistência** nos critérios de avaliação
- **Agilidade** no preenchimento de formulários
- **Foco** nos dados de entrada importantes

### ✅ **Para Gestores**
- **Dados confiáveis** para análise de performance
- **Padronização** de critérios em toda a operação
- **Relatórios precisos** de pontualidade
- **Identificação automática** de causas de atraso

### ✅ **Para o Sistema**
- **Integridade** dos dados garantida
- **Performance** otimizada com cálculos locais
- **Manutenibilidade** centralizada das regras
- **Escalabilidade** para novas regras de cálculo

## 🚀 **Futuras Melhorias**

### 📈 **Recursos Planejados**
- **Cálculo de KPIs** automáticos (pontualidade, eficiência)
- **Alertas proativos** para atrasos previstos
- **Análise de tendências** de performance
- **Sugestões automáticas** de melhorias

### 🎯 **Regras Adicionais**
- **Tolerância de atraso** configurável por cliente
- **Cálculo de multas** por atraso automático
- **Performance por motorista** e equipamento
- **Benchmarking** entre operações similares

---

**Sistema desenvolvido para garantir precisão, consistência e eficiência na gestão de operações logísticas.**
