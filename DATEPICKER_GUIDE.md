# 📅 DateTimePicker - <PERSON><PERSON><PERSON> de Uso

## 🎯 Visão Geral

O DateTimePicker é um componente customizado que facilita o preenchimento de campos de data/hora no sistema de operações logísticas. Ele combina um input de texto com um calendário interativo e botões de ação rápida.

## ✨ Funcionalidades

### 📝 **Entrada Manual**
- **Formato brasileiro**: DD/MM/YYYY HH:mm:ss
- **Validação automática** durante a digitação
- **Feedback visual** para formatos inválidos

### 📅 **Calendário Interativo**
- **Seleção visual** de data e hora
- **Intervalos de 15 minutos** para facilitar seleção
- **Interface intuitiva** com navegação por mês/ano

### ⚡ **Botões de Ação Rápida**
- **📅 Calendário**: Abre/fecha o seletor visual
- **🕐 Agora**: Define data/hora atual automaticamente
- **❌ Limpar**: Remove o valor do campo

## 🎨 **Interface do Usuário**

### 🔧 **Componentes Visuais**
```
[Input de texto] [📅] [🕐] [❌]
     ↓
[Calendário popup quando ativado]
```

### 🎯 **Estados do Campo**
- **Normal**: Campo editável com botões ativos
- **Somente leitura**: Fundo acinzentado, sem botões
- **Desabilitado**: Campo e botões inativos

## 📋 **Como Usar**

### 1. **Entrada Manual**
```
Digite diretamente no formato:
05/06/2025 14:30:00
15/12/2024 09:15:30
01/01/2025 00:00:00
```

### 2. **Seleção Visual**
1. Clique no ícone **📅** para abrir o calendário
2. Navegue pelos meses usando as setas
3. Clique na data desejada
4. Selecione a hora na lista lateral
5. O campo será preenchido automaticamente

### 3. **Ações Rápidas**
- **🕐 Agora**: Preenche com data/hora atual
- **❌ Limpar**: Remove todo o conteúdo
- **📅 Calendário**: Alterna exibição do seletor

## 🔄 **Integração com Cálculos**

### ⚡ **Cálculo Automático**
Quando você preenche campos de data/hora, o sistema automaticamente:

1. **Valida o formato** da data/hora
2. **Calcula performance** (NO PRAZO/ATRASADO)
3. **Calcula tempo decorrido** entre eventos
4. **Atualiza campos relacionados** em tempo real

### 📊 **Campos Afetados**
- **Performance de Carregamento**
- **Performance de Descarga**
- **Tempo em Carga**
- **Tempo de Descarga**
- **Causa do Atraso**
- **Diárias C** (Carregamento)
- **Diárias D** (Descarregamento)

## 🎯 **Exemplos Práticos**

### 🟢 **Cenário 1: Operação Pontual**
```
1. Agendamento: 05/06/2025 07:00:00
2. Clique em "🕐 Agora" para chegada: 05/06/2025 06:45:00
3. Sistema calcula: "NO PRAZO"
```

### 🔴 **Cenário 2: Operação Atrasada**
```
1. Agendamento: 05/06/2025 07:00:00
2. Use calendário para chegada: 05/06/2025 08:30:00
3. Sistema calcula: "ATRASADO"
```

### ⏱️ **Cenário 3: Cálculo de Tempo**
```
1. Início Carga: 07:00:00
2. Fim Carga: 09:30:00
3. Sistema calcula: "2h 30min"
```

### 💰 **Cenário 4: Cálculo de Diárias**
```
1. Agendamento: 05/06/2025 07:00:00
2. Chegada: 05/06/2025 06:45:00 (adiantado)
3. Início Carga: 05/06/2025 07:30:00 (cliente demorou)
4. Fim Carga: 05/06/2025 10:00:00
5. Sistema calcula Diárias C: "3h" (30min atraso + 2h30min carga)
```

## 🛠️ **Recursos Técnicos**

### 📁 **Arquivo Principal**
```typescript
// src/components/ui/datetime-picker.tsx
<DateTimePicker
  value={formData.dataHoraChegadaCarga}
  onChange={(value) => handleDateTimeChange('dataHoraChegadaCarga', value)}
  placeholder="05/06/2025 06:30:00"
/>
```

### 🎨 **Estilos Customizados**
- **Tema consistente** com o design system
- **Cores adaptadas** ao modo claro/escuro
- **Responsividade** para dispositivos móveis

### 🔧 **Props Disponíveis**
```typescript
interface DateTimePickerProps {
  id?: string;              // ID do campo
  name?: string;            // Nome do campo
  value?: string;           // Valor atual
  onChange?: (value: string) => void; // Callback de mudança
  placeholder?: string;     // Texto de exemplo
  className?: string;       // Classes CSS adicionais
  disabled?: boolean;       // Campo desabilitado
  readOnly?: boolean;       // Somente leitura
}
```

## 📱 **Responsividade**

### 💻 **Desktop**
- Calendário popup posicionado abaixo do campo
- Botões de ação visíveis e acessíveis
- Navegação completa por teclado

### 📱 **Mobile**
- Interface adaptada para toque
- Botões com tamanho adequado
- Calendário otimizado para telas pequenas

## 🎯 **Benefícios**

### ✅ **Para Usuários**
- **Facilidade de uso** com múltiplas formas de entrada
- **Redução de erros** com validação automática
- **Agilidade** com botões de ação rápida
- **Flexibilidade** entre entrada manual e visual

### ✅ **Para o Sistema**
- **Consistência** no formato de dados
- **Integração** com cálculos automáticos
- **Validação** em tempo real
- **Experiência** de usuário aprimorada

## 🔮 **Futuras Melhorias**

### 📈 **Recursos Planejados**
- **Sugestões inteligentes** baseadas em histórico
- **Atalhos de teclado** para ações comuns
- **Validação de horário comercial** por cliente
- **Templates** de horários frequentes

### 🎨 **Melhorias de UX**
- **Animações suaves** para transições
- **Feedback haptic** em dispositivos móveis
- **Modo escuro** otimizado
- **Acessibilidade** aprimorada

## 🚀 **Dicas de Uso**

### ⚡ **Atalhos Rápidos**
- **Tab**: Navegar entre campos
- **Enter**: Confirmar seleção no calendário
- **Esc**: Fechar calendário
- **Espaço**: Abrir calendário quando focado

### 📝 **Boas Práticas**
1. **Use "Agora"** para registros em tempo real
2. **Valide visualmente** antes de salvar
3. **Aproveite o calendário** para datas futuras
4. **Mantenha consistência** no formato

### 🎯 **Casos de Uso Comuns**
- **Agendamentos**: Use calendário para datas futuras
- **Chegadas**: Use "Agora" para registros imediatos
- **Planejamento**: Combine calendário + ajuste manual
- **Correções**: Edite diretamente no campo de texto

---

**Desenvolvido para maximizar a eficiência e precisão no preenchimento de dados temporais no sistema de logística.**
