# 🚛 Sistema de Logística de Transporte B2B

## 📋 Visão Geral

Sistema web completo para gerenciamento de frota de caminhões e operações logísticas para empresas de transporte B2B. O sistema foi desenvolvido para atender especificamente empresas que trabalham com grandes clientes como **Novelis**, **Pepsico** e **Ambev**, oferecendo controle completo desde o agendamento até a entrega final.

## 🏗️ Arquitetura Técnica

### Stack Tecnológico
- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Framework**: Shadcn/ui + Tailwind CSS
- **Roteamento**: React Router DOM
- **Formulários**: React Hook Form + Zod (validação)
- **Gráficos**: Recharts
- **Notificações**: Sonner + Toast
- **Ícones**: Lucide React
- **DatePicker**: React DatePicker + Date-fns
- **Cálculos Automáticos**: Sistema próprio de cálculos em tempo real

### Estrutura do Projeto
```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes base (shadcn/ui + DateTimePicker)
│   ├── dashboard/      # Componentes do dashboard
│   ├── operations/     # Componentes de operações (com colunas customizáveis)
│   ├── gas/            # Componentes de abastecimento (Cavalo)
│   ├── maintenance/    # Componentes de manutenção (Cavalo)
│   └── operators/      # Componentes de motoristas
├── pages/              # Páginas principais
├── types/              # Definições TypeScript
├── hooks/              # Custom hooks
├── lib/                # Utilitários
└── index.css          # Estilos globais e variáveis CSS
```

## 🎨 Sistema de Design

### Paleta de Cores (Simplificada)
O sistema utiliza uma paleta de cores profissional e discreta:

- **Primary**: `220 13% 35%` (cinza-azulado neutro)
- **Status Operational**: `142 30% 45%` (verde suave)
- **Status Maintenance**: `45 60% 50%` (âmbar discreto)
- **Status Warning**: `0 50% 50%` (vermelho suave)
- **Sidebar**: `0 0% 98%` (branco quase puro)

### Princípios de Design
- **Cores Menos Chamativas**: Redução de saturação para ambiente corporativo
- **Consistência Visual**: Uso de variáveis CSS customizadas
- **Responsividade**: Design adaptável para desktop, tablet e mobile
- **Acessibilidade**: Contraste adequado e navegação por teclado

## 📊 Funcionalidades Principais

### 1. Dashboard Principal (`/`)
- **Métricas de Frota**: Total, operacionais, em manutenção, parados
- **Documentação de Transporte**:
  - CTE Emitidos (Conhecimento de Transporte Eletrônico)
  - Sem CTE (alerta crítico)
  - NF Emitidas (Notas Fiscais)
- **Operação Atual**: Operações ativas, manutenções pendentes
- **Cards de Destaque**: Visualização dos principais caminhões

### 2. Gerenciamento de Caminhões (`/forklifts`)
- **CRUD Completo**: Criar, visualizar, editar, excluir
- **Tipos**: Gás, Elétrica, Retrátil
- **Status**: Operacional, Manutenção, Parado
- **Filtros**: Por status e tipo
- **Informações**: Modelo, capacidade, horímetro, última manutenção

### 3. Gerenciamento de Motoristas (`/operators`)
- **Cadastro Completo**: Nome, CPF, contato, turno, função
- **Certificações**: ASO (Atestado de Saúde Ocupacional) e NR-11
- **Status de Validade**: Regular, próximo do vencimento, vencido
- **Alertas**: Notificações para renovações

### 4. Operações Logísticas (`/operations`) - **FUNCIONALIDADE PRINCIPAL**

#### 🆕 **Colunas Customizáveis**
- **Interface Personalizável**: Usuário escolhe quais colunas exibir
- **47 Campos Organizados**: Por categorias (Identificação, Agendamento, etc.)
- **Persistência**: Configurações salvas no localStorage
- **Filtros Inteligentes**: Por categoria ou campo específico

#### 🧮 **Cálculos Automáticos**
- **Performance**: NO PRAZO/ATRASADO calculado automaticamente
- **Tempos**: Duração de carregamento/descarga em tempo real
- **Diárias C/D**: Cálculo automático de atrasos do cliente
- **Validação**: Formato de data/hora brasileiro

#### 📅 **DateTimePicker Integrado**
- **Entrada Múltipla**: Manual, calendário visual, botões rápidos
- **Formato Brasileiro**: DD/MM/YYYY HH:mm:ss
- **Ações Rápidas**: "Agora", "Limpar", calendário popup
- **Integração**: Cálculos automáticos ao preencher

#### Estrutura de Dados Baseada na Planilha Operacional
O sistema replica exatamente a planilha utilizada pela operação, com 47 campos organizados:

**Clientes Suportados:**
- Novelis
- Pepsico
- Ambev

**Modalidades:**
- Frota (própria)
- Terceiro
- Agregado

#### Interface de Operações
- **Filtros**: Por cliente e modalidade
- **Busca**: Por motorista, chave, produto, cidades
- **Cards Ativos**: Operações em andamento
- **Tabela Completa**: Todas as operações com informações essenciais

#### Detalhes da Operação (6 Abas Organizadas)

**Aba 1 - Geral:**
- Identificação (Chave, Cliente, ID/DT)
- Informações do motorista e equipamento
- Rota completa (origem/destino com UF)
- Produto e peso
- Agendamento de carregamento/descarga
- Status da operação

**Aba 2 - Carregamento:**
- App Cliente - Carga
- Horários (chegada, início, fim)
- Performance e tempo em carga
- Status final do carregamento
- Causas de atraso
- Diárias e ocorrências

**Aba 3 - Descarga:**
- App Cliente - Descarga
- Horários de descarga
- Performance e tempo
- Status (houve descarga, avaria, devolução)
- Devolução de pallet
- Comprovante de descarga

**Aba 4 - Financeiro:**
- Frete Empresa NET
- Frete Terceiro/Agregado
- Margens por modalidade
- Resumo financeiro

**Aba 5 - Documentos:**
- Número da Nota Fiscal
- Número do CTE
- Status de emissão
- Alertas sobre documentação obrigatória
- **IMPORTANTE**: Aviso sobre CTE obrigatório

**Aba 6 - Observações:**
- Campo livre para anotações
- Resumo consolidado da operação
- Informações adicionais
- Histórico de status

### 5. Controle de Manutenção (`/maintenance`) - **Cavalo do Caminhão**
- **Solicitações**: Registro de problemas do cavalo
- **Status**: Aguardando, em andamento, concluído
- **Histórico**: Por cavalo e período
- **Responsáveis**: Quem reportou e executou
- **Terminologia Atualizada**: "Cavalo" ao invés de "Empilhadeira"

### 6. Controle de Abastecimento (`/gas-supply`) - **Cavalo do Caminhão**
- **Registro**: Data, quantidade, horímetro do cavalo
- **Eficiência**: Consumo por hora do cavalo
- **Estatísticas**: Total, quantidade, eficiência média
- **Terminologia Atualizada**: "Cavalo" ao invés de "Empilhadeira"

### 7. Relatórios (`/reports`)
- **Utilização**: Horas de uso por caminhão
- **Manutenção**: Histórico e custos
- **Consumo**: Eficiência de combustível
- **Operadores**: Status de certificações

## 🆕 Funcionalidades Avançadas Implementadas

### 📊 **Sistema de Colunas Customizáveis**
- **47 Campos Organizados**: Por 10 categorias diferentes
- **Interface Intuitiva**: Seleção por categoria ou individual
- **Persistência**: Configurações salvas automaticamente
- **Restauração**: Botão para voltar ao padrão
- **Contador Visual**: Mostra quantas colunas estão selecionadas

### 🧮 **Cálculos Automáticos Avançados**

#### **Performance de Carregamento/Descarga**
- **NO PRAZO**: Se chegada ≤ horário agendado
- **ATRASADO**: Se chegada > horário agendado
- **Automático**: Calculado em tempo real

#### **Diárias C (Carregamento)**
- **Atraso para Iniciar**: Se cliente demorou para começar
- **Tempo Total**: Do início ao fim do carregamento
- **Fórmula**: Atraso + Tempo de Carregamento

#### **Diárias D (Descarregamento)**
- **Herança**: Considera atrasos do carregamento
- **Atraso para Iniciar**: Ajustado pela herança
- **Tempo Total**: Do início ao fim da descarga
- **Fórmula**: Atraso (com herança) + Tempo de Descarga

### 📅 **DateTimePicker Profissional**
- **Entrada Manual**: Formato brasileiro DD/MM/YYYY HH:mm:ss
- **Calendário Visual**: Interface intuitiva para seleção
- **Botões Rápidos**: "Agora", "Limpar", calendário
- **Validação**: Formato automático em tempo real
- **Integração**: Dispara cálculos automáticos

### 🚛 **Terminologia Corrigida**
- **Cavalo do Caminhão**: Substituição de "Empilhadeira" por "Cavalo"
- **Abastecimento**: Referente ao cavalo do caminhão
- **Manutenção**: Referente ao cavalo do caminhão
- **Consistência**: Terminologia adequada ao contexto logístico

## 🔧 Configuração e Instalação

### Pré-requisitos
- Node.js 18+
- npm ou yarn

### Instalação
```bash
# Clone o repositório
git clone [url-do-repositorio]

# Instale as dependências
npm install

# Dependências principais adicionadas:
# - react-datepicker: DateTimePicker profissional
# - @types/react-datepicker: Tipos TypeScript
# - date-fns: Utilitários de data

# Execute em desenvolvimento
npm run dev

# Build para produção
npm run build
```

### Variáveis de Ambiente
Não há variáveis de ambiente específicas. O sistema roda com dados mock para demonstração.

## 📱 Responsividade

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Adaptações
- **Sidebar**: Colapsível em mobile
- **Tabelas**: Scroll horizontal em telas pequenas
- **Cards**: Grid responsivo
- **Diálogos**: Altura adaptável mantendo usabilidade

## 🎯 Especificações Técnicas Importantes

### Sistema de Tipos (TypeScript)
```typescript
// Principais interfaces
interface Operation {
  chave: string;
  clientePagador: ClienteEnum;
  produto: string;
  motorista: string;
  // ... 47 campos da planilha
}

enum ClienteEnum {
  NOVELIS = "Novelis",
  PEPSICO = "Pepsico",
  AMBEV = "Ambev"
}
```

### Gerenciamento de Estado
- **Estado Local**: useState para componentes
- **Dados Mock**: Simulação de API para demonstração
- **Formulários**: React Hook Form com validação Zod

### Padrões de Código
- **Componentes**: Functional components com hooks
- **Props**: Interfaces TypeScript bem definidas
- **Estilos**: Tailwind CSS com variáveis customizadas
- **Nomenclatura**: camelCase para JS, kebab-case para CSS

## 🚨 Pontos Críticos do Negócio

### 1. CTE (Conhecimento de Transporte Eletrônico)
- **OBRIGATÓRIO** para transporte de cargas
- **Problema Comum**: Motoristas saem sem aguardar emissão
- **Métrica Crítica**: "Sem CTE" no dashboard
- **Alerta**: Sistema avisa sobre obrigatoriedade

### 2. Documentação Fiscal
- **Nota Fiscal**: Obrigatória para mercadorias
- **Rastreabilidade**: Controle completo de emissão
- **Compliance**: Evita problemas fiscais

### 3. Modalidades de Transporte
- **Frota**: Veículos próprios da empresa
- **Terceiro**: Transportadoras contratadas
- **Agregado**: Motoristas autônomos

### 4. Clientes Estratégicos
- **Novelis**: Alumínio e produtos metálicos
- **Pepsico**: Produtos alimentícios e bebidas
- **Ambev**: Bebidas e produtos relacionados

## 🔄 Fluxo Operacional

### 1. Criação da Operação
1. Cadastro com dados básicos (cliente, produto, rota)
2. Definição de motorista e equipamento
3. Agendamento de carregamento/descarga
4. Geração de códigos e documentos

### 2. Execução
1. Chegada para carregamento
2. Processo de carga com horários
3. Emissão de documentos (NF, CTE)
4. Transporte até destino
5. Processo de descarga
6. Finalização com comprovantes

### 3. Controle
1. Acompanhamento em tempo real
2. Registro de performance
3. Controle de atrasos
4. Documentação de ocorrências

## 📈 Métricas e KPIs

### Dashboard Principal
- **Total de Caminhões**: Frota disponível
- **Status Operacional**: Distribuição por status
- **CTE Emitidos**: Documentos de transporte
- **Sem CTE**: Operações críticas
- **NF Emitidas**: Documentação fiscal

### Operacionais
- **Tempo de Carregamento**: Performance por operação
- **Atrasos**: Causas e frequência
- **Eficiência**: Consumo de combustível
- **Utilização**: Horas de uso dos equipamentos

## 🛠️ Manutenção e Evolução

### Estrutura Preparada Para:
- **Integração com APIs**: Substituição de dados mock
- **Novos Clientes**: Enum expansível
- **Campos Adicionais**: Interface flexível
- **Relatórios Avançados**: Base de dados estruturada
- **Mobile App**: Componentes responsivos

### Pontos de Atenção:
- **Performance**: Paginação para grandes volumes
- **Validações**: Regras de negócio específicas por cliente
- **Backup**: Dados críticos de operação
- **Auditoria**: Log de alterações importantes

## 📞 Contexto de Negócio

Este sistema foi desenvolvido especificamente para empresas de logística B2B que:
- Trabalham com grandes clientes industriais
- Precisam de controle rigoroso de documentação
- Gerenciam frotas próprias e terceirizadas
- Requerem rastreabilidade completa das operações
- Necessitam de compliance fiscal e regulatório

O foco principal está no controle operacional detalhado, substituindo planilhas manuais por um sistema integrado e profissional.

## 📋 Campos da Planilha Implementados (47 campos)

### Identificação e Básicos
- Chave
- Data/Hora Agendada P/ Carregamento
- Data/Hora Agendada P/ Descarga
- Produto
- Cliente-Pagador
- ID/B100/SVN/Nº DT

### Rota e Transporte
- Cliente - Cidade Origem
- UF Origem
- Cliente - Cidade Destino
- UF Destino
- Peso
- Motorista
- Modalidade
- Dias em Rota
- Modelo/Equipamento

### Operação e Códigos
- Operação Cadastrada?
- Codigo da Operação
- Nº Rota
- Nº Solicitação de Carga
- Placa/Tração
- Programador

### Financeiro
- Frete Empresa NET
- Frete terceiro
- Margem terceiro
- Frete Agregado
- Margem Agregado

### Carregamento
- App Cliente - Carga
- Data/Hora Chegada Carga
- Data/Hora Inicio de Carga
- Data/hora Fim de Carregamento
- Tempo em Carga
- Status Final do Carregamento
- Diarias C
- Performance de Carregamento
- Causa do ATRAZO
- Nº Ocorrencia de Carga

### Documentação
- Nº Nota Fiscal
- Número CTE

### Descarga
- App Cliente - Descarga
- Data/Hora Chegada P/Descarga
- Data/hora Inicio de Descarga
- Data/hora Fim de Descarga
- Tempo de Descarga
- Status Final
- Performance de Descarga
- Causa do ATRAZO
- Diarias D
- Número de Ocorrencia Descarga
- Houve Descarga?
- Devolução de Pallet
- Houve Avaria?
- Houve Devolução?
- Comprovante de descarga

### Observações
- Observação

## 🎨 Interface Design Principles

### Diálogos com Tamanho Fixo
- **Altura Fixa**: 85vh para consistência visual
- **Scroll Interno**: Cada aba tem scroll independente
- **Layout Flexbox**: Estrutura bem definida
- **Footer Fixo**: Botões sempre visíveis

### Cores Profissionais
- **Saturação Reduzida**: Cores menos chamativas
- **Variáveis CSS**: Fácil manutenção
- **Modo Escuro**: Suporte completo
- **Acessibilidade**: Contraste adequado

## 🚀 Como Começar o Desenvolvimento

### 1. Entendimento do Negócio
- Estude o fluxo de operações logísticas
- Compreenda a importância do CTE
- Familiarize-se com os clientes (Novelis, Pepsico, Ambev)

### 2. Estrutura de Dados
- Examine `src/types/index.ts` para entender as interfaces
- Veja os dados mock em `src/pages/Operations.tsx`
- Compreenda a relação entre os 47 campos da planilha

### 3. Componentes Principais
- **OperationDetails.tsx**: Diálogo com 6 abas
- **OperationDialog.tsx**: Formulário de criação/edição
- **DashboardOverview.tsx**: Métricas principais

### 4. Próximos Passos Sugeridos
- Integração com API real
- Implementação de autenticação
- Relatórios avançados
- Notificações em tempo real
- Mobile responsiveness aprimorado

## 📚 Documentação Adicional

### Arquivos de Documentação Específica
- **`COLUNAS_CUSTOMIZAVEIS.md`**: Guia completo do sistema de colunas customizáveis
- **`CALCULOS_AUTOMATICOS.md`**: Documentação dos cálculos de performance e tempos
- **`CALCULOS_DIARIAS.md`**: Explicação detalhada do sistema de diárias C/D
- **`DATEPICKER_GUIDE.md`**: Manual de uso do DateTimePicker

### Funcionalidades Documentadas
- **Colunas Customizáveis**: 47 campos organizados em 10 categorias
- **Cálculos Automáticos**: Performance, tempos e diárias
- **DateTimePicker**: Entrada múltipla com validação
- **Terminologia**: Cavalo do caminhão vs empilhadeira

### Exemplos Práticos
Cada documentação inclui:
- **Cenários reais** de uso
- **Exemplos práticos** com dados
- **Regras de negócio** detalhadas
- **Benefícios** para usuários e gestores

---

**Desenvolvido para empresas de logística B2B que precisam de controle operacional completo e profissional.**
